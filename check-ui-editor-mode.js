import axios from 'axios';
import testConfig from './test-config.js';

// 在 UI 编辑模式下检查页面状态
async function checkUIEditorMode() {
  console.log('🔍 在 UI 编辑模式下检查页面状态\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    
    // 1. 获取当前页面 Schema
    console.log('📋 1. 获取当前页面 Schema');
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = schemaResponse.data.data;
    
    console.log('📊 当前页面完整结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 2. 分析页面结构
    console.log('\n📋 2. 分析页面结构');
    
    const pageSchema = currentSchema.schema;
    if (pageSchema) {
      console.log('✅ 页面 Schema 存在');
      console.log(`   - 类型: ${pageSchema.type}`);
      console.log(`   - 组件: ${pageSchema['x-component']}`);
      console.log(`   - 属性数量: ${Object.keys(pageSchema.properties || {}).length}`);
      
      if (pageSchema.properties) {
        console.log('\n🔍 页面属性详情:');
        Object.keys(pageSchema.properties).forEach(key => {
          const prop = pageSchema.properties[key];
          console.log(`\n🔸 属性: ${key}`);
          console.log(`   - 组件: ${prop['x-component']}`);
          console.log(`   - UID: ${prop['x-uid'] || 'none'}`);
          console.log(`   - 初始化器: ${prop['x-initializer'] || 'none'}`);
          
          if (prop.properties) {
            console.log(`   - 子属性数量: ${Object.keys(prop.properties).length}`);
            
            if (Object.keys(prop.properties).length > 0) {
              console.log('   📋 子属性:');
              Object.keys(prop.properties).forEach(subKey => {
                const subProp = prop.properties[subKey];
                console.log(`     - ${subKey}: ${subProp['x-component']} (UID: ${subProp['x-uid'] || 'none'})`);
                
                // 检查是否是我们的测试区块
                if (subKey.includes('working-block-') || subKey.includes('correct-block-') || subKey.includes('test-block-')) {
                  console.log('       🎯 这是我们的测试区块!');
                  console.log(`       📊 区块详情:`);
                  console.log(`         - 标题: ${subProp['x-component-props']?.title || 'no title'}`);
                  console.log(`         - 内容组件数量: ${Object.keys(subProp.properties || {}).length}`);
                }
              });
            }
          }
        });
      }
    } else {
      console.log('❌ 页面 Schema 不存在');
    }
    
    // 3. 检查是否有我们的测试区块
    console.log('\n📋 3. 检查测试区块');
    
    const schemaString = JSON.stringify(currentSchema);
    const hasWorkingBlock = schemaString.includes('working-block-');
    const hasCorrectBlock = schemaString.includes('correct-block-');
    const hasTestBlock = schemaString.includes('test-block-');
    const hasAPITest = schemaString.includes('API 测试');
    const hasSuccessMessage = schemaString.includes('成功！');
    
    console.log('🔍 测试区块检查结果:');
    console.log(`   - 包含 working-block: ${hasWorkingBlock}`);
    console.log(`   - 包含 correct-block: ${hasCorrectBlock}`);
    console.log(`   - 包含 test-block: ${hasTestBlock}`);
    console.log(`   - 包含 API 测试内容: ${hasAPITest}`);
    console.log(`   - 包含成功消息: ${hasSuccessMessage}`);
    
    // 4. 分析为什么区块没有在 UI 编辑模式下显示
    console.log('\n📋 4. 分析 UI 编辑模式下的渲染问题');
    
    if (hasWorkingBlock || hasCorrectBlock || hasTestBlock) {
      console.log('🎯 区块存在于 Schema 中，但没有在 UI 中显示');
      console.log('\n可能的原因:');
      console.log('1. 🔧 组件注册问题 - CardItem 或 Markdown.Void 组件没有正确注册');
      console.log('2. 🎨 CSS 样式问题 - 区块被隐藏或样式异常');
      console.log('3. 🔄 前端缓存问题 - 需要清除缓存或重启');
      console.log('4. 📦 依赖问题 - 缺少必要的插件或依赖');
      console.log('5. 🏗️ 结构问题 - 区块结构不符合 NocoBase 的期望');
      
      // 检查区块结构是否完整
      const gridProp = pageSchema?.properties?.grid;
      if (gridProp && gridProp.properties) {
        const blockKeys = Object.keys(gridProp.properties).filter(key => 
          key.includes('working-block-') || key.includes('correct-block-') || key.includes('test-block-')
        );
        
        if (blockKeys.length > 0) {
          console.log('\n🔍 检查区块结构完整性:');
          blockKeys.forEach(blockKey => {
            const block = gridProp.properties[blockKey];
            console.log(`\n📊 区块: ${blockKey}`);
            console.log(`   - 有 type: ${!!block.type}`);
            console.log(`   - 有 name: ${!!block.name}`);
            console.log(`   - 有 x-uid: ${!!block['x-uid']}`);
            console.log(`   - 有 x-component: ${!!block['x-component']}`);
            console.log(`   - 有 x-component-props: ${!!block['x-component-props']}`);
            console.log(`   - 有 properties: ${!!block.properties}`);
            
            if (block.properties) {
              console.log(`   - 子组件数量: ${Object.keys(block.properties).length}`);
              Object.keys(block.properties).forEach(subKey => {
                const subComp = block.properties[subKey];
                console.log(`     - ${subKey}: ${subComp['x-component']} (有内容: ${!!subComp['x-component-props']?.content})`);
              });
            }
          });
        }
      }
    } else {
      console.log('❌ 没有找到任何测试区块');
    }
    
    // 5. 建议下一步行动
    console.log('\n📋 5. 建议下一步行动');
    
    console.log('🚀 为了解决渲染问题，建议:');
    console.log('1. 🔍 检查 NocoBase 的组件注册机制');
    console.log('2. 📋 对比手动创建的区块和我们 API 创建的区块的差异');
    console.log('3. 🛠️ 尝试使用 NocoBase 的官方区块创建 API');
    console.log('4. 🔄 检查是否需要触发前端重新渲染');
    console.log('5. 📦 验证所需的插件和组件是否已加载');

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行检查
checkUIEditorMode().catch(console.error);
