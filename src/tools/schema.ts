import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";

export async function registerSchemaTools(server: <PERSON><PERSON>p<PERSON>erver, client: NocoBaseClient) {
  // Get collection schema
  server.registerTool(
    "get_collection_schema",
    {
      title: "Get Collection Schema",
      description: "Get the complete schema definition for a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection")
      }
    },
    async ({ collection }) => {
      try {
        const collectionData = await client.getCollection(collection);

        let schema = `Schema for collection '${collection}':\n\n`;
        schema += `Name: ${collectionData.name}\n`;
        schema += `Title: ${collectionData.title || 'No title'}\n`;
        schema += `Description: ${collectionData.description || 'No description'}\n`;
        schema += `Auto-generated ID: ${collectionData.autoGenId ? 'Yes' : 'No'}\n`;
        schema += `Timestamps: ${collectionData.createdAt ? 'Created' : ''}${collectionData.updatedAt ? (collectionData.createdAt ? ', Updated' : 'Updated') : ''}\n`;
        schema += `User tracking: ${collectionData.createdBy ? 'Created by' : ''}${collectionData.updatedBy ? (collectionData.createdBy ? ', Updated by' : 'Updated by') : ''}\n`;
        schema += `Hidden: ${collectionData.hidden ? 'Yes' : 'No'}\n`;
        schema += `Inherits: ${collectionData.inherit ? 'Yes' : 'No'}\n\n`;

        if (collectionData.fields && collectionData.fields.length > 0) {
          schema += `Fields (${collectionData.fields.length}):\n\n`;
          
          collectionData.fields.forEach((field, index) => {
            schema += `${index + 1}. ${field.name}\n`;
            schema += `   Type: ${field.type}\n`;
            schema += `   Interface: ${field.interface || 'None'}\n`;
            schema += `   Description: ${field.description || 'No description'}\n`;
            
            if (field.uiSchema) {
              schema += `   UI Schema:\n`;
              Object.entries(field.uiSchema).forEach(([key, value]) => {
                schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
              });
            }
            
            // Show other important field properties
            const otherProps = Object.entries(field).filter(([key]) => 
              !['key', 'name', 'type', 'interface', 'description', 'uiSchema', 'collectionName'].includes(key)
            );
            
            if (otherProps.length > 0) {
              schema += `   Other properties:\n`;
              otherProps.forEach(([key, value]) => {
                if (value !== null && value !== undefined) {
                  schema += `     ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}\n`;
                }
              });
            }
            
            schema += '\n';
          });
        } else {
          schema += 'No fields defined.\n';
        }

        return {
          content: [{
            type: "text",
            text: schema
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting schema for collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // List fields
  server.registerTool(
    "list_fields",
    {
      title: "List Collection Fields",
      description: "List all fields in a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection")
      }
    },
    async ({ collection }) => {
      try {
        const fields = await client.listFields(collection);

        let response = `Fields in collection '${collection}' (${fields.length} total):\n\n`;

        if (fields.length > 0) {
          fields.forEach((field, index) => {
            response += `${index + 1}. ${field.name}\n`;
            response += `   Type: ${field.type}\n`;
            response += `   Interface: ${field.interface || 'None'}\n`;
            if (field.description) {
              response += `   Description: ${field.description}\n`;
            }
            response += '\n';
          });
        } else {
          response += 'No fields found.\n';
        }

        return {
          content: [{
            type: "text",
            text: response
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing fields for collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create field
  server.registerTool(
    "create_field",
    {
      title: "Create Field",
      description: "Add a new field to a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        name: z.string().describe("Name of the field"),
        type: z.string().describe("Field type (e.g., 'string', 'integer', 'boolean', 'date')"),
        interface: z.string().optional().describe("UI interface type (e.g., 'input', 'textarea', 'select')"),
        description: z.string().optional().describe("Field description"),
        uiSchema: z.any().optional().describe("UI schema configuration (JSON object)")
      }
    },
    async ({ collection, name, type, interface: fieldInterface, description, uiSchema }) => {
      try {
        const fieldData: any = {
          name,
          type,
          description
        };

        if (fieldInterface) {
          fieldData.interface = fieldInterface;
        }

        if (uiSchema) {
          fieldData.uiSchema = uiSchema;
        }

        const field = await client.createField(collection, fieldData);

        return {
          content: [{
            type: "text",
            text: `Successfully created field '${field.name}' in collection '${collection}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating field '${name}' in collection '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get page schema structure
  server.registerTool(
    "analyze_page_schema",
    {
      title: "Analyze Page Schema Structure",
      description: "Get the complete UI schema structure for a page, showing all blocks and their layout",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the page (e.g., 'cafh7yoyd6w')")
      }
    },
    async ({ schemaUid }) => {
      try {
        const schema = await client.getPageSchema(schemaUid);

        let response = `Page Schema Structure for '${schemaUid}':\n\n`;

        // Helper function to recursively analyze schema structure
        function analyzeSchema(obj: any, depth: number = 0, key?: string): string {
          const indent = '  '.repeat(depth);
          let result = '';

          if (obj && typeof obj === 'object') {
            // Show the key name if provided
            if (key) {
              result += `${indent}🔑 ${key}:\n`;
            }

            // Check for component type
            if (obj['x-component']) {
              const componentIcon = getComponentIcon(obj['x-component']);
              result += `${indent}${componentIcon} Component: ${obj['x-component']}\n`;

              if (obj['x-uid']) {
                result += `${indent}   📋 UID: ${obj['x-uid']}\n`;
              }

              if (obj.title) {
                result += `${indent}   🏷️  Title: ${obj.title}\n`;
              }

              if (obj['x-component-props']) {
                const props = obj['x-component-props'];
                if (Object.keys(props).length > 0) {
                  result += `${indent}   ⚙️  Props:\n`;
                  for (const [propKey, propValue] of Object.entries(props)) {
                    if (propValue !== null && propValue !== undefined) {
                      const displayValue = typeof propValue === 'object'
                        ? JSON.stringify(propValue).substring(0, 100) + (JSON.stringify(propValue).length > 100 ? '...' : '')
                        : String(propValue);
                      result += `${indent}      ${propKey}: ${displayValue}\n`;
                    }
                  }
                }
              }
            }

            // Check for decorator
            if (obj['x-decorator']) {
              result += `${indent}🎨 Decorator: ${obj['x-decorator']}\n`;
              if (obj['x-decorator-props']) {
                const props = obj['x-decorator-props'];
                if (Object.keys(props).length > 0) {
                  result += `${indent}   ⚙️  Decorator Props:\n`;
                  for (const [propKey, propValue] of Object.entries(props)) {
                    if (propValue !== null && propValue !== undefined) {
                      const displayValue = typeof propValue === 'object'
                        ? JSON.stringify(propValue).substring(0, 100) + (JSON.stringify(propValue).length > 100 ? '...' : '')
                        : String(propValue);
                      result += `${indent}      ${propKey}: ${displayValue}\n`;
                    }
                  }
                }
              }
            }

            // Check for collection binding
            if (obj['x-collection-field']) {
              result += `${indent}🗂️  Collection Field: ${obj['x-collection-field']}\n`;
            }

            // Check for data source
            if (obj['x-data-source']) {
              result += `${indent}📊 Data Source: ${obj['x-data-source']}\n`;
            }

            // Check for ACL action
            if (obj['x-acl-action']) {
              result += `${indent}🔐 ACL Action: ${obj['x-acl-action']}\n`;
            }

            // Check for action type
            if (obj['x-action']) {
              result += `${indent}⚡ Action: ${obj['x-action']}\n`;
            }

            // Check for settings
            if (obj['x-settings']) {
              result += `${indent}🛠️  Settings: ${obj['x-settings']}\n`;
            }

            // Check for initializer
            if (obj['x-initializer']) {
              result += `${indent}🚀 Initializer: ${obj['x-initializer']}\n`;
            }

            // Check for properties (child components)
            if (obj.properties && typeof obj.properties === 'object') {
              const propCount = Object.keys(obj.properties).length;
              result += `${indent}📁 Properties (${propCount}):\n`;
              for (const [propKey, propValue] of Object.entries(obj.properties)) {
                result += analyzeSchema(propValue, depth + 1, propKey);
              }
            }

            result += '\n';
          }

          return result;
        }

        // Helper function to get component icon
        function getComponentIcon(component: string): string {
          const iconMap: { [key: string]: string } = {
            'Page': '📄',
            'Grid': '🏗️',
            'Grid.Row': '📏',
            'Grid.Col': '📐',
            'CardItem': '🃏',
            'TableV2': '📊',
            'TableV2.Column': '📋',
            'FormV2': '📝',
            'FormItem': '📝',
            'CollectionField': '🏷️',
            'Action': '🔘',
            'Action.Link': '🔗',
            'ActionBar': '🎛️',
            'Markdown.Void': '📝',
            'Iframe': '🖼️',
            'ChartCardItem': '📈',
            'Details': '📋',
            'Tabs': '📑',
            'Tabs.TabPane': '📄',
            'Space': '🔲'
          };
          return iconMap[component] || '📦';
        }

        response += analyzeSchema(schema);

        return {
          content: [{
            type: "text",
            text: response
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting page schema '${schemaUid}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get schema properties (blocks)
  server.registerTool(
    "get_schema_properties",
    {
      title: "Get Schema Properties",
      description: "Get the properties (child blocks) of a specific schema component",
      inputSchema: {
        schemaUid: z.string().describe("Schema UID of the component")
      }
    },
    async ({ schemaUid }) => {
      try {
        const properties = await client.getSchemaProperties(schemaUid);

        let response = `Schema Properties for '${schemaUid}':\n\n`;

        if (properties && typeof properties === 'object') {
          for (const [key, value] of Object.entries(properties)) {
            response += `🔧 Property: ${key}\n`;
            if (value && typeof value === 'object') {
              const prop = value as any;
              if (prop['x-component']) {
                response += `   Component: ${prop['x-component']}\n`;
              }
              if (prop['x-decorator']) {
                response += `   Decorator: ${prop['x-decorator']}\n`;
              }
              if (prop['x-uid']) {
                response += `   UID: ${prop['x-uid']}\n`;
              }
              if (prop['x-collection-field']) {
                response += `   Collection Field: ${prop['x-collection-field']}\n`;
              }
              if (prop['x-data-source']) {
                response += `   Data Source: ${prop['x-data-source']}\n`;
              }
            }
            response += '\n';
          }
        } else {
          response += 'No properties found or invalid response.\n';
        }

        return {
          content: [{
            type: "text",
            text: response
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting schema properties for '${schemaUid}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
