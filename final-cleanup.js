import axios from 'axios';
import testConfig from './test-config.js';

// 最终清理 - 删除剩余的不需要的路由
async function finalCleanup() {
  console.log('🧹 最终清理 - 删除剩余的不需要的路由\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 1. 获取当前路由状态
    console.log('📋 1. 获取当前路由状态');
    const routesResponse = await client.get('/desktopRoutes:list');
    const routes = routesResponse.data.data;
    
    console.log(`✅ 当前有 ${routes.length} 个路由:`);
    routes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid}`);
    });
    
    // 2. 手动删除特定的路由
    console.log('\n📋 2. 手动删除特定的路由');
    
    const routesToDelete = [
      { id: 12, title: "MCP 测试页面", schemaUid: "page-1754235700602-luqwmsxu9" },
      { id: 40, title: "null", schemaUid: "v4pv8gtz2s3" }
    ];
    
    for (const route of routesToDelete) {
      try {
        console.log(`🗑️ 删除路由: ID ${route.id}, "${route.title}"`);
        
        // 先删除对应的 UI Schema
        if (route.schemaUid) {
          try {
            await client.post('/uiSchemas:remove', {
              'x-uid': route.schemaUid
            });
            console.log(`   ✅ 删除 UI Schema: ${route.schemaUid}`);
          } catch (schemaError) {
            console.log(`   ⚠️ 删除 UI Schema 失败: ${schemaError.response?.data?.message || schemaError.message}`);
          }
        }
        
        // 然后删除路由 - 使用不同的方法
        try {
          await client.delete(`/desktopRoutes/${route.id}`);
          console.log(`   ✅ 删除路由成功 (DELETE): ID ${route.id}`);
        } catch (deleteError) {
          // 如果 DELETE 失败，尝试 POST destroy
          try {
            await client.post(`/desktopRoutes:destroy`, {
              filter: { id: route.id }
            });
            console.log(`   ✅ 删除路由成功 (POST destroy): ID ${route.id}`);
          } catch (postError) {
            console.log(`   ❌ 删除路由失败: ID ${route.id}`);
            console.log(`      DELETE 错误: ${deleteError.response?.data?.message || deleteError.message}`);
            console.log(`      POST 错误: ${postError.response?.data?.message || postError.message}`);
          }
        }
        
      } catch (error) {
        console.log(`   ❌ 处理路由失败: ID ${route.id}, 错误: ${error.message}`);
      }
    }
    
    // 3. 验证最终结果
    console.log('\n📋 3. 验证最终结果');
    
    const finalRoutesResponse = await client.get('/desktopRoutes:list');
    const finalRoutes = finalRoutesResponse.data.data;
    
    console.log(`✅ 最终剩余 ${finalRoutes.length} 个路由:`);
    finalRoutes.forEach(route => {
      console.log(`   - ID: ${route.id}, 标题: "${route.title}", Schema UID: ${route.schemaUid}`);
    });
    
    // 检查是否只剩下"123"页面
    const keepRouteUid = '2mk30f1pasa';
    const keepRoute = finalRoutes.find(route => route.schemaUid === keepRouteUid);
    const otherRoutes = finalRoutes.filter(route => route.schemaUid !== keepRouteUid);
    
    if (keepRoute && otherRoutes.length === 0) {
      console.log('🎉 完美！现在只剩下手工创建的"123"页面');
    } else if (keepRoute) {
      console.log(`⚠️ "123"页面还在，但还有 ${otherRoutes.length} 个其他路由`);
      otherRoutes.forEach(route => {
        console.log(`   - 其他路由: ID ${route.id}, "${route.title}"`);
      });
    } else {
      console.log('❌ "123"页面似乎被意外删除了');
    }
    
    console.log('\n🎯 最终清理完成！');

  } catch (error) {
    console.error('❌ 最终清理失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行最终清理
finalCleanup().catch(console.error);
