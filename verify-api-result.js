import axios from 'axios';
import testConfig from './test-config.js';

// 验证 API 调用的实际结果
async function verifyAPIResult() {
  console.log('🔍 验证 API 调用的实际结果\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    
    // 1. 获取当前页面 Schema
    console.log('📋 1. 获取当前页面 Schema');
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = schemaResponse.data.data;
    
    console.log('📊 当前页面结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 2. 检查是否有我们的测试区块
    const schemaString = JSON.stringify(currentSchema);
    const hasTestBlock = schemaString.includes('test-block-');
    const hasAPITest = schemaString.includes('API 测试');
    
    console.log('\n🔍 区块检查结果:');
    console.log(`   - 包含测试区块 UID: ${hasTestBlock}`);
    console.log(`   - 包含 API 测试内容: ${hasAPITest}`);
    
    if (hasTestBlock || hasAPITest) {
      console.log('✅ 找到测试区块内容!');
      
      // 提取测试区块相关的内容
      const testBlockMatches = schemaString.match(/test-block-\d+/g);
      if (testBlockMatches) {
        console.log('🎯 找到的测试区块 UID:');
        testBlockMatches.forEach(uid => console.log(`   - ${uid}`));
      }
    } else {
      console.log('❌ 没有找到测试区块');
    }
    
    // 3. 分析页面结构的详细信息
    console.log('\n📋 2. 详细分析页面结构');
    
    if (currentSchema.schema && currentSchema.schema.properties) {
      console.log('📊 页面属性分析:');
      Object.keys(currentSchema.schema.properties).forEach(key => {
        const prop = currentSchema.schema.properties[key];
        console.log(`\n🔸 属性: ${key}`);
        console.log(`   - 组件: ${prop['x-component']}`);
        console.log(`   - UID: ${prop['x-uid'] || 'none'}`);
        
        if (prop.properties) {
          console.log(`   - 子属性数量: ${Object.keys(prop.properties).length}`);
          Object.keys(prop.properties).forEach(subKey => {
            const subProp = prop.properties[subKey];
            console.log(`     - ${subKey}: ${subProp['x-component']} (UID: ${subProp['x-uid'] || 'none'})`);
          });
        }
      });
    }
    
    // 4. 尝试理解为什么区块没有显示
    console.log('\n📋 3. 分析为什么区块没有显示');
    
    // 检查 Grid 结构
    const gridProp = currentSchema.schema?.properties?.grid;
    if (gridProp) {
      console.log('✅ 找到 Grid 组件:');
      console.log(`   - 组件: ${gridProp['x-component']}`);
      console.log(`   - 初始化器: ${gridProp['x-initializer']}`);
      console.log(`   - UID: ${gridProp['x-uid'] || 'none'}`);
      
      if (gridProp.properties) {
        console.log(`   - Grid 子属性数量: ${Object.keys(gridProp.properties).length}`);
        
        if (Object.keys(gridProp.properties).length > 0) {
          console.log('🔍 Grid 子属性详情:');
          Object.keys(gridProp.properties).forEach(key => {
            const prop = gridProp.properties[key];
            console.log(`     - ${key}: ${prop['x-component']} (UID: ${prop['x-uid'] || 'none'})`);
            
            // 检查是否是我们的测试区块
            if (key.includes('test-block-') || JSON.stringify(prop).includes('API 测试')) {
              console.log('       🎯 这是我们的测试区块!');
              console.log(`       📊 区块详情: ${JSON.stringify(prop, null, 8)}`);
            }
          });
        } else {
          console.log('⚠️ Grid 没有子属性，这可能是问题所在');
        }
      } else {
        console.log('⚠️ Grid 没有 properties 属性');
      }
    } else {
      console.log('❌ 没有找到 Grid 组件');
    }
    
    // 5. 检查是否有其他可能的容器
    console.log('\n📋 4. 检查其他可能的容器');
    
    function findAllComponents(obj, path = '') {
      const components = [];
      if (obj && typeof obj === 'object') {
        if (obj['x-component']) {
          components.push({
            path,
            component: obj['x-component'],
            uid: obj['x-uid'],
            hasProperties: !!obj.properties
          });
        }
        if (obj.properties) {
          Object.keys(obj.properties).forEach(key => {
            components.push(...findAllComponents(obj.properties[key], `${path}.${key}`));
          });
        }
      }
      return components;
    }
    
    const allComponents = findAllComponents(currentSchema);
    console.log('🔍 页面中的所有组件:');
    allComponents.forEach(comp => {
      console.log(`   - ${comp.path}: ${comp.component} (UID: ${comp.uid || 'none'}, 有子属性: ${comp.hasProperties})`);
    });
    
    // 6. 最终诊断
    console.log('\n📋 5. 最终诊断');
    
    if (hasTestBlock || hasAPITest) {
      console.log('🎯 诊断结果: 区块已添加到 Schema 中，但可能存在以下问题:');
      console.log('   1. 区块结构不正确，NocoBase 无法渲染');
      console.log('   2. 缺少必要的组件或装饰器');
      console.log('   3. 区块被添加到了错误的位置');
      console.log('   4. 前端缓存问题');
    } else {
      console.log('❌ 诊断结果: 区块没有被正确添加到 Schema 中');
      console.log('   可能的原因:');
      console.log('   1. API 调用实际上失败了');
      console.log('   2. 区块被添加到了其他地方');
      console.log('   3. Schema 更新没有持久化');
    }

  } catch (error) {
    console.error('❌ 验证失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行验证
verifyAPIResult().catch(console.error);
