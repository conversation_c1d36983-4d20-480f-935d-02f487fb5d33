import axios from 'axios';
import testConfig from './test-config.js';

// 直接测试 NocoBase API 调用
async function debugApiCall() {
  console.log('🔍 直接调试 NocoBase API 调用\n');

  // 创建 axios 客户端
  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 1. 首先获取页面 Schema
    const pageUid = 'page-1754235700602-luqwmsxu9';
    console.log(`📋 Step 1: Get page schema for ${pageUid}`);
    
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    console.log('✅ Schema response status:', schemaResponse.status);
    console.log('📄 Schema data:', JSON.stringify(schemaResponse.data, null, 2));

    // 2. 创建一个简单的区块 Schema
    console.log('\n📋 Step 2: Create simple block schema');
    
    const simpleBlockSchema = {
      type: 'void',
      'x-component': 'CardItem',
      'x-component-props': {
        title: '🔍 API 调试测试区块'
      },
      properties: {
        'markdown-content': {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: '# API 调试测试\n\n这是一个直接通过 API 创建的测试区块。'
          }
        }
      }
    };

    console.log('📊 Block schema:', JSON.stringify(simpleBlockSchema, null, 2));

    // 3. 尝试不同的 API 调用方式
    console.log('\n📋 Step 3: Try different API approaches');

    // 方式 1: 直接插入到页面
    console.log('\n🔸 Approach 1: Insert directly to page');
    try {
      const insertResponse1 = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: pageUid,
        schema: simpleBlockSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Direct insert success:', insertResponse1.status);
      console.log('📥 Response:', JSON.stringify(insertResponse1.data, null, 2));
    } catch (error) {
      console.log('❌ Direct insert failed:', error.response?.status, error.response?.data || error.message);
    }

    // 方式 2: 插入到 grid 路径
    console.log('\n🔸 Approach 2: Insert to grid path');
    try {
      const insertResponse2 = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: `${pageUid}.grid`,
        schema: simpleBlockSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Grid insert success:', insertResponse2.status);
      console.log('📥 Response:', JSON.stringify(insertResponse2.data, null, 2));
    } catch (error) {
      console.log('❌ Grid insert failed:', error.response?.status, error.response?.data || error.message);
    }

    // 方式 3: 使用 patch 方法
    console.log('\n🔸 Approach 3: Use patch method');
    try {
      const patchResponse = await client.post('/uiSchemas:patch', {
        'x-uid': pageUid,
        schema: {
          properties: {
            [`test-block-${Date.now()}`]: simpleBlockSchema
          }
        }
      });
      console.log('✅ Patch success:', patchResponse.status);
      console.log('📥 Response:', JSON.stringify(patchResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Patch failed:', error.response?.status, error.response?.data || error.message);
    }

    // 4. 检查页面是否有变化
    console.log('\n📋 Step 4: Check if page changed');
    try {
      const updatedSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      console.log('✅ Updated schema retrieved');
      
      const originalSchema = schemaResponse.data;
      const updatedSchema = updatedSchemaResponse.data;
      
      console.log('🔍 Schema comparison:');
      console.log('Original properties count:', Object.keys(originalSchema.data?.properties || {}).length);
      console.log('Updated properties count:', Object.keys(updatedSchema.data?.properties || {}).length);
      
      if (JSON.stringify(originalSchema) !== JSON.stringify(updatedSchema)) {
        console.log('✅ Schema has changed!');
      } else {
        console.log('⚠️ Schema unchanged');
      }
      
    } catch (error) {
      console.log('❌ Failed to check updated schema:', error.message);
    }

    // 5. 尝试列出所有可用的 API 端点
    console.log('\n📋 Step 5: Check available API endpoints');
    try {
      const apiResponse = await client.get('/');
      console.log('✅ API root response:', apiResponse.status);
      // console.log('📄 Available endpoints:', JSON.stringify(apiResponse.data, null, 2));
    } catch (error) {
      console.log('❌ API root failed:', error.response?.status, error.message);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n🎯 调试完成！');
  console.log('请检查以上输出来了解 NocoBase API 的具体要求。');
}

// 运行调试
debugApiCall().catch(console.error);
